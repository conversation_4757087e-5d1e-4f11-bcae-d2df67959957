<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // إضافة حقول التقييم لجدول listings
        Schema::table('listings', function (Blueprint $table) {
            $table->decimal('rating_avg', 3, 2)->default(0)->after('status');
            $table->integer('rating_count')->default(0)->after('rating_avg');
        });

        // إضافة حقول التقييم لجدول users
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('rating_avg', 3, 2)->default(0)->after('is_verified');
            $table->integer('rating_count')->default(0)->after('rating_avg');
        });
    }

    public function down()
    {
        Schema::table('listings', function (Blueprint $table) {
            $table->dropColumn(['rating_avg', 'rating_count']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['rating_avg', 'rating_count']);
        });
    }
};
