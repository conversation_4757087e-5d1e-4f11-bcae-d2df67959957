# ⚙️ دليل التثبيت والإعداد - Mega Platform

## 📋 فهرس المحتويات
- [متطلبات النظام](#متطلبات-النظام)
- [التثبيت المحلي](#التثبيت-المحلي)
- [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
- [إعداد البيئة](#إعداد-البيئة)
- [تشغيل المشروع](#تشغيل-المشروع)
- [إعداد الإنتاج](#إعداد-الإنتاج)
- [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 💻 متطلبات النظام

### متطلبات أساسية
- **PHP**: 8.1 أو أحدث
- **Composer**: 2.0 أو أحدث
- **Node.js**: 16.0 أو أحدث
- **NPM**: 8.0 أو أحدث

### قاعدة البيانات
- **MySQL**: 5.7 أو أحدث
- **أو MariaDB**: 10.3 أو أحدث

### خادم الويب
- **Apache**: 2.4 أو أحدث
- **أو Nginx**: 1.18 أو أحدث

### PHP Extensions المطلوبة
```bash
# تحقق من وجود Extensions
php -m | grep -E "(openssl|pdo|mbstring|tokenizer|xml|ctype|json|bcmath|curl|fileinfo|gd)"
```

**Extensions مطلوبة:**
- OpenSSL PHP Extension
- PDO PHP Extension
- Mbstring PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
- Ctype PHP Extension
- JSON PHP Extension
- BCMath PHP Extension
- cURL PHP Extension
- Fileinfo PHP Extension
- GD PHP Extension

---

## 🚀 التثبيت المحلي

### 1. استنساخ المشروع

```bash
# إذا كان المشروع في Git repository
git clone https://github.com/your-repo/mega-platform.git
cd mega-platform

# أو إذا كان لديك ملفات المشروع
cd /path/to/mega-platform
```

### 2. تثبيت Dependencies

#### تثبيت PHP Dependencies
```bash
composer install
```

#### تثبيت JavaScript Dependencies
```bash
npm install
```

### 3. إعداد ملف البيئة

```bash
# نسخ ملف البيئة المثال
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate
```

### 4. تحرير ملف .env

```bash
# فتح ملف البيئة للتحرير
nano .env
# أو
code .env
```

**إعدادات أساسية:**
```env
APP_NAME="Mega Platform"
APP_ENV=local
APP_KEY=base64:your-generated-key-here
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mega_platform
DB_USERNAME=root
DB_PASSWORD=your_password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

---

## 🗄️ إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات

#### MySQL/MariaDB
```sql
-- الاتصال بـ MySQL
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE mega_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'mega_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON mega_platform.* TO 'mega_user'@'localhost';
FLUSH PRIVILEGES;

-- الخروج
EXIT;
```

### 2. تشغيل Migrations

```bash
# تشغيل جميع migrations
php artisan migrate

# تشغيل migrations مع تأكيد
php artisan migrate --force
```

### 3. تشغيل Seeders (اختياري)

```bash
# تشغيل جميع seeders
php artisan db:seed

# تشغيل seeder محدد
php artisan db:seed --class=CategorySeeder
```

### 4. إنشاء Storage Links

```bash
# إنشاء symbolic link للتخزين
php artisan storage:link
```

---

## 🔧 إعداد البيئة

### 1. إعدادات الملفات

#### إعداد صلاحيات الملفات (Linux/Mac)
```bash
# إعطاء صلاحيات الكتابة
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# إعداد المالك (إذا لزم الأمر)
sudo chown -R www-data:www-data storage
sudo chown -R www-data:www-data bootstrap/cache
```

#### إعداد صلاحيات الملفات (Windows)
```cmd
# تأكد من أن المجلدات قابلة للكتابة
icacls storage /grant Everyone:F /T
icacls bootstrap\cache /grant Everyone:F /T
```

### 2. بناء Frontend Assets

```bash
# للتطوير
npm run dev

# للإنتاج
npm run build

# مراقبة التغييرات (للتطوير)
npm run dev -- --watch
```

### 3. إعداد Queue Workers (اختياري)

```bash
# تشغيل queue worker
php artisan queue:work

# تشغيل في الخلفية (Linux/Mac)
nohup php artisan queue:work > /dev/null 2>&1 &
```

---

## 🏃‍♂️ تشغيل المشروع

### 1. تشغيل خادم التطوير

```bash
# تشغيل Laravel development server
php artisan serve

# تشغيل على port مخصص
php artisan serve --port=8080

# تشغيل على host مخصص
php artisan serve --host=0.0.0.0 --port=8000
```

### 2. تشغيل Vite (للتطوير)

```bash
# في terminal منفصل
npm run dev
```

### 3. الوصول للتطبيق

- **الموقع الرئيسي**: http://localhost:8000
- **لوحة الإدارة**: http://localhost:8000/admin
- **API Base URL**: http://localhost:8000/api

### 4. حسابات افتراضية (إذا تم تشغيل seeders)

```
Admin:
Email: <EMAIL>
Password: password

Provider:
Email: <EMAIL>
Password: password

User:
Email: <EMAIL>
Password: password
```

---

## 🌐 إعداد الإنتاج

### 1. متطلبات الإنتاج

```bash
# تحديث Composer للإنتاج
composer install --optimize-autoloader --no-dev

# بناء assets للإنتاج
npm run build

# تحسين التطبيق
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 2. إعدادات البيئة للإنتاج

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# إعدادات قاعدة البيانات الإنتاج
DB_CONNECTION=mysql
DB_HOST=your-production-host
DB_PORT=3306
DB_DATABASE=your-production-db
DB_USERNAME=your-production-user
DB_PASSWORD=your-secure-password

# إعدادات البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls

# إعدادات التخزين
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-bucket-name
```

### 3. إعداد خادم الويب

#### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com;
    root /var/www/mega-platform/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "Class not found"
```bash
# إعادة تحميل autoloader
composer dump-autoload

# مسح cache
php artisan cache:clear
php artisan config:clear
```

#### 2. خطأ في قاعدة البيانات
```bash
# التحقق من الاتصال
php artisan tinker
>>> DB::connection()->getPdo();

# إعادة تشغيل migrations
php artisan migrate:fresh
```

#### 3. مشاكل الصلاحيات
```bash
# Linux/Mac
sudo chown -R $USER:www-data storage
sudo chown -R $USER:www-data bootstrap/cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

#### 4. مشاكل Assets
```bash
# مسح cache
npm run build
php artisan view:clear

# إعادة إنشاء storage link
php artisan storage:link
```

### سجلات الأخطاء

```bash
# عرض آخر سجلات الأخطاء
tail -f storage/logs/laravel.log

# عرض سجلات محددة
php artisan log:show
```

### أدوات التشخيص

```bash
# معلومات النظام
php artisan about

# فحص التكوين
php artisan config:show

# فحص الروتس
php artisan route:list

# فحص قاعدة البيانات
php artisan migrate:status
```

---

## 📞 الدعم

للحصول على المساعدة:
- 📧 **Email**: <EMAIL>
- 📖 **Documentation**: [docs/README.md](./README.md)
- 🐛 **Issues**: GitHub Issues

---

*آخر تحديث: يناير 2025*
