<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'type',
        'is_verified',
        'rating_avg',
        'rating_count',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_verified' => 'boolean',
    ];

    // Relationships
    public function listings()
    {
        return $this->hasMany(Listing::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'user_id');
    }

    public function providerOrders()
    {
        return $this->hasMany(Order::class, 'provider_id');
    }



  public function ratingsGiven()
{
    return $this->hasMany(Rating::class, 'reviewer_id');
}

public function ratingsReceived()
{
    return $this->hasMany(Rating::class, 'reviewed_id');
}





    public function wishlist()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistProducts()
    {
        return $this->belongsToMany(Listing::class, 'wishlists', 'user_id', 'listing_id')
                    ->withTimestamps();
    }

    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function interests()
    {
        return $this->hasMany(AdTargeting::class);
    }

    public function driverProfile()
    {
        return $this->hasOne(Driver::class);
    }

    // Helper methods
    public function isAdmin()
    {
        return $this->type === 'admin';
    }

    public function isProvider()
    {
        return $this->type === 'provider';
    }

    public function isDriver()
    {
        return $this->type === 'driver';
    }

    public function isRegularUser()
    {
        return $this->type === 'user';
    }

    public function averageRating()
    {
        return $this->ratingsReceived()->avg('rating') ?? 0;
    }
    public function interestedCategories()
    {
        return $this->belongsToMany(
            Category::class,
            'ads_targeting',   // اسم الجدول الوسيط
            'user_id',         // المفتاح الذي يشير إلى المستخدم
            'category_id'      // المفتاح الذي يشير إلى التصنيف
        )->withTimestamps();   // إذا كنت تستخدم timestamps
    }
    
public function receivedRatings()
{
    return $this->hasMany(Rating::class, 'provider_id')
        ->where('is_approved', true); // فقط التقييمات المعتمدة
}


public function ratings()
{
    return $this->hasMany(Rating::class, 'provider_id');
}






}