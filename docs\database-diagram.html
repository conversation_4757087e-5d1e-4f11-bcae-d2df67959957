<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مخطط قاعدة البيانات - Mega Platform</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .diagram-container {
            background: #fafafa;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .table-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .table-header {
            background: #4a90e2;
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 1.1em;
        }
        .table-content {
            padding: 15px;
        }
        .field {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .field:last-child {
            border-bottom: none;
        }
        .field-name {
            font-weight: 500;
            color: #333;
        }
        .field-type {
            color: #666;
            font-size: 0.9em;
        }
        .primary-key {
            color: #e74c3c;
            font-weight: bold;
        }
        .foreign-key {
            color: #3498db;
            font-weight: bold;
        }
        .relationships {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .relationship-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .relationship-type {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .relationship-desc {
            color: #666;
            font-size: 0.95em;
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
        }
        @media (max-width: 768px) {
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
            .tables-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ مخطط قاعدة البيانات</h1>
            <p>Mega Platform - نظام التجارة الإلكترونية المتكامل</p>
        </div>
        
        <div class="content">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">18</div>
                    <div class="stat-label">جدول رئيسي</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">علاقة بين الجداول</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">فهرس محسن</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">أنواع المستخدمين</div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
                    erDiagram
                        users ||--o{ listings : "يملك"
                        users ||--o{ orders : "يطلب"
                        users ||--o{ orders : "يوفر"
                        users ||--o{ orders : "يوصل"
                        users ||--o{ ratings : "يقيم"
                        users ||--o{ ratings : "يُقيم"
                        users ||--o{ transactions : "ينفذ"
                        users ||--o{ drivers : "ملف_سائق"
                        users ||--o{ wishlists : "يفضل"
                        
                        categories ||--o{ categories : "فئة_فرعية"
                        categories ||--o{ listings : "تصنف"
                        
                        listings ||--o{ listing_images : "صور"
                        listings ||--o{ orders : "يُطلب"
                        listings ||--o{ ratings : "يُقيم"
                        listings ||--o{ wishlists : "مفضل"
                        
                        orders ||--o{ transactions : "دفع"
                        orders ||--o{ ratings : "تقييم_طلب"
                        
                        users {
                            bigint id PK
                            string name
                            string email UK
                            string phone
                            timestamp email_verified_at
                            string password
                            enum type
                            boolean is_verified
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        categories {
                            bigint id PK
                            string name
                            bigint parent_id FK
                            string icon
                            boolean is_active
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        listings {
                            bigint id PK
                            bigint user_id FK
                            string title
                            text description
                            bigint category_id FK
                            string country
                            string city
                            string location
                            decimal price
                            boolean is_promoted
                            enum status
                            timestamp promoted_until
                            integer views
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        listing_images {
                            bigint id PK
                            bigint listing_id FK
                            string image_url
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        orders {
                            bigint id PK
                            bigint listing_id FK
                            bigint user_id FK
                            bigint provider_id FK
                            bigint driver_id FK
                            enum status
                            text delivery_address
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        ratings {
                            bigint id PK
                            bigint reviewer_id FK
                            bigint reviewed_id FK
                            bigint listing_id FK
                            bigint order_id FK
                            integer rating
                            text comment
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        transactions {
                            bigint id PK
                            bigint user_id FK
                            decimal amount
                            enum type
                            enum status
                            string payment_method
                            string transaction_reference
                            timestamp created_at
                            timestamp updated_at
                        }
                        
                        wishlists {
                            bigint id PK
                            bigint user_id FK
                            bigint listing_id FK
                            timestamp created_at
                            timestamp updated_at
                        }
                </div>
            </div>

            <h2>📋 الجداول الرئيسية</h2>
            <div class="tables-grid">
                <div class="table-card">
                    <div class="table-header">👥 users - المستخدمين</div>
                    <div class="table-content">
                        <div class="field">
                            <span class="field-name primary-key">id</span>
                            <span class="field-type">BIGINT PK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">name</span>
                            <span class="field-type">VARCHAR(255)</span>
                        </div>
                        <div class="field">
                            <span class="field-name">email</span>
                            <span class="field-type">VARCHAR(255) UNIQUE</span>
                        </div>
                        <div class="field">
                            <span class="field-name">phone</span>
                            <span class="field-type">VARCHAR(255)</span>
                        </div>
                        <div class="field">
                            <span class="field-name">type</span>
                            <span class="field-type">ENUM</span>
                        </div>
                        <div class="field">
                            <span class="field-name">is_verified</span>
                            <span class="field-type">BOOLEAN</span>
                        </div>
                    </div>
                </div>

                <div class="table-card">
                    <div class="table-header">🏷️ categories - الفئات</div>
                    <div class="table-content">
                        <div class="field">
                            <span class="field-name primary-key">id</span>
                            <span class="field-type">BIGINT PK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">name</span>
                            <span class="field-type">VARCHAR(255)</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">parent_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">icon</span>
                            <span class="field-type">VARCHAR(255)</span>
                        </div>
                        <div class="field">
                            <span class="field-name">is_active</span>
                            <span class="field-type">BOOLEAN</span>
                        </div>
                    </div>
                </div>

                <div class="table-card">
                    <div class="table-header">📦 listings - المنتجات</div>
                    <div class="table-content">
                        <div class="field">
                            <span class="field-name primary-key">id</span>
                            <span class="field-type">BIGINT PK</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">user_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">title</span>
                            <span class="field-type">VARCHAR(255)</span>
                        </div>
                        <div class="field">
                            <span class="field-name">description</span>
                            <span class="field-type">TEXT</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">category_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">price</span>
                            <span class="field-type">DECIMAL(10,2)</span>
                        </div>
                        <div class="field">
                            <span class="field-name">status</span>
                            <span class="field-type">ENUM</span>
                        </div>
                    </div>
                </div>

                <div class="table-card">
                    <div class="table-header">🛒 orders - الطلبات</div>
                    <div class="table-content">
                        <div class="field">
                            <span class="field-name primary-key">id</span>
                            <span class="field-type">BIGINT PK</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">listing_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">user_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">provider_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name foreign-key">driver_id</span>
                            <span class="field-type">BIGINT FK</span>
                        </div>
                        <div class="field">
                            <span class="field-name">status</span>
                            <span class="field-type">ENUM</span>
                        </div>
                    </div>
                </div>
            </div>

            <h2>🔗 العلاقات الرئيسية</h2>
            <div class="relationships">
                <div class="relationship-item">
                    <div class="relationship-type">One-to-Many: User → Listings</div>
                    <div class="relationship-desc">مستخدم واحد (بائع) يمكن أن يملك عدة منتجات</div>
                </div>
                <div class="relationship-item">
                    <div class="relationship-type">One-to-Many: Category → Listings</div>
                    <div class="relationship-desc">فئة واحدة تحتوي على عدة منتجات</div>
                </div>
                <div class="relationship-item">
                    <div class="relationship-type">One-to-Many: Listing → Images</div>
                    <div class="relationship-desc">منتج واحد يمكن أن يحتوي على عدة صور</div>
                </div>
                <div class="relationship-item">
                    <div class="relationship-type">Many-to-Many: User ↔ Listings (Wishlist)</div>
                    <div class="relationship-desc">مستخدم يمكن أن يضع عدة منتجات في المفضلة، والمنتج يمكن أن يكون في مفضلة عدة مستخدمين</div>
                </div>
                <div class="relationship-item">
                    <div class="relationship-type">Self-Referencing: Category → Category</div>
                    <div class="relationship-desc">الفئات يمكن أن تحتوي على فئات فرعية (parent/child relationship)</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا المخطط بواسطة Claude 4 Sonnet - Augment Agent</p>
            <p>آخر تحديث: يناير 2025</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#4a90e2',
                primaryTextColor: '#333',
                primaryBorderColor: '#4a90e2',
                lineColor: '#666',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e9ecef'
            }
        });
    </script>
</body>
</html>
