# 📱 دليل ربط تطبيقات الموبايل - Mega Platform

## 📋 فهرس المحتويات
- [نظرة عامة](#نظرة-عامة)
- [Android Integration](#android-integration)
- [iOS Integration](#ios-integration)
- [Flutter Integration](#flutter-integration)
- [معالجة الأخطاء](#معالجة-الأخطاء)
- [أفضل الممارسات](#أفضل-الممارسات)

---

## 🌟 نظرة عامة

هذا الدليل يوضح كيفية ربط تطبيقات الموبايل مع Mega Platform API باستخدام Laravel Sanctum للمصادقة.

### متطلبات أساسية
- **Base URL**: `https://yourdomain.com/api/`
- **Authentication**: Bear<PERSON> (Laravel Sanctum)
- **Content-Type**: `application/json`
- **Rate Limit**: 60 requests/minute

---

## 🤖 Android Integration

### 1. إعداد Dependencies

#### build.gradle (Module: app)
```gradle
dependencies {
    // Retrofit for API calls
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
    
    // Coroutines for async operations
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    
    // Image loading
    implementation 'com.github.bumptech.glide:glide:4.15.1'
}
```

### 2. إنشاء Data Classes

#### User.kt
```kotlin
data class User(
    val id: Int,
    val name: String,
    val email: String,
    val phone: String?,
    val type: String,
    val isVerified: Boolean,
    val createdAt: String
)
```

#### Product.kt
```kotlin
data class Product(
    val id: Int,
    val title: String,
    val description: String,
    val price: String,
    val categoryId: Int,
    val city: String,
    val location: String,
    val status: String,
    val isPromoted: Boolean,
    val images: List<ProductImage>,
    val category: Category?,
    val createdAt: String
)

data class ProductImage(
    val id: Int,
    val imageUrl: String
)

data class Category(
    val id: Int,
    val name: String,
    val icon: String?
)
```

#### API Response Classes
```kotlin
data class ApiResponse<T>(
    val success: Boolean,
    val message: String?,
    val data: T?,
    val errors: Map<String, List<String>>?
)

data class LoginResponse(
    val success: Boolean,
    val message: String,
    val data: LoginData
)

data class LoginData(
    val token: String,
    val user: User
)
```

### 3. إنشاء API Interface

#### ApiService.kt
```kotlin
import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    
    // Authentication
    @POST("login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @POST("register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<User>>
    
    @POST("logout")
    suspend fun logout(): Response<ApiResponse<String>>
    
    @GET("user")
    suspend fun getCurrentUser(): Response<User>
    
    // Categories
    @GET("categories")
    suspend fun getCategories(): Response<ApiResponse<List<Category>>>
    
    @GET("categories/{categoryId}/listings")
    suspend fun getCategoryProducts(@Path("categoryId") categoryId: Int): Response<ApiResponse<CategoryProductsData>>
    
    // Products
    @GET("products/featured")
    suspend fun getFeaturedProducts(): Response<ApiResponse<List<Product>>>
    
    @GET("provider/products")
    suspend fun getProviderProducts(): Response<ApiResponse<List<Product>>>
    
    @Multipart
    @POST("provider/products")
    suspend fun createProduct(
        @Part("title") title: RequestBody,
        @Part("description") description: RequestBody,
        @Part("price") price: RequestBody,
        @Part("category_id") categoryId: RequestBody,
        @Part("city") city: RequestBody,
        @Part("location") location: RequestBody,
        @Part images: List<MultipartBody.Part>
    ): Response<ApiResponse<Product>>
    
    @DELETE("provider/products/{productId}")
    suspend fun deleteProduct(@Path("productId") productId: Int): Response<ApiResponse<String>>
}
```

### 4. إنشاء API Client

#### ApiClient.kt
```kotlin
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object ApiClient {
    private const val BASE_URL = "https://yourdomain.com/api/"
    
    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    
    private val authInterceptor = Interceptor { chain ->
        val token = TokenManager.getToken() // إنشاء TokenManager منفصل
        val request = chain.request().newBuilder()
            .addHeader("Accept", "application/json")
            .addHeader("Content-Type", "application/json")
            .apply {
                if (!token.isNullOrEmpty()) {
                    addHeader("Authorization", "Bearer $token")
                }
            }
            .build()
        chain.proceed(request)
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(authInterceptor)
        .addInterceptor(loggingInterceptor)
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    val apiService: ApiService = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(ApiService::class.java)
}
```

### 5. إدارة Tokens

#### TokenManager.kt
```kotlin
import android.content.Context
import android.content.SharedPreferences

object TokenManager {
    private const val PREF_NAME = "mega_prefs"
    private const val TOKEN_KEY = "auth_token"
    private const val USER_KEY = "user_data"
    
    private lateinit var prefs: SharedPreferences
    
    fun init(context: Context) {
        prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    fun saveToken(token: String) {
        prefs.edit().putString(TOKEN_KEY, token).apply()
    }
    
    fun getToken(): String? {
        return prefs.getString(TOKEN_KEY, null)
    }
    
    fun saveUser(user: User) {
        val gson = Gson()
        val userJson = gson.toJson(user)
        prefs.edit().putString(USER_KEY, userJson).apply()
    }
    
    fun getUser(): User? {
        val userJson = prefs.getString(USER_KEY, null)
        return if (userJson != null) {
            val gson = Gson()
            gson.fromJson(userJson, User::class.java)
        } else null
    }
    
    fun clearAll() {
        prefs.edit().clear().apply()
    }
    
    fun isLoggedIn(): Boolean {
        return !getToken().isNullOrEmpty()
    }
}
```

### 6. Repository Pattern

#### AuthRepository.kt
```kotlin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class AuthRepository {
    
    suspend fun login(email: String, password: String): Result<LoginData> {
        return withContext(Dispatchers.IO) {
            try {
                val request = LoginRequest(email, password)
                val response = ApiClient.apiService.login(request)
                
                if (response.isSuccessful && response.body()?.success == true) {
                    val loginData = response.body()!!.data
                    TokenManager.saveToken(loginData.token)
                    TokenManager.saveUser(loginData.user)
                    Result.success(loginData)
                } else {
                    val errorMessage = response.body()?.message ?: "Login failed"
                    Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    suspend fun register(
        name: String,
        email: String,
        phone: String,
        password: String,
        type: String
    ): Result<User> {
        return withContext(Dispatchers.IO) {
            try {
                val request = RegisterRequest(name, email, phone, password, password, type)
                val response = ApiClient.apiService.register(request)
                
                if (response.isSuccessful && response.body()?.success == true) {
                    val user = response.body()!!.data!!
                    Result.success(user)
                } else {
                    val errorMessage = response.body()?.message ?: "Registration failed"
                    Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    suspend fun logout(): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val response = ApiClient.apiService.logout()
                TokenManager.clearAll()
                
                if (response.isSuccessful) {
                    Result.success("Logged out successfully")
                } else {
                    Result.failure(Exception("Logout failed"))
                }
            } catch (e: Exception) {
                TokenManager.clearAll() // Clear local data anyway
                Result.success("Logged out locally")
            }
        }
    }
}
```

### 7. ViewModel Example

#### AuthViewModel.kt
```kotlin
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class AuthViewModel : ViewModel() {
    private val repository = AuthRepository()

    private val _loginState = MutableStateFlow<UiState<LoginData>>(UiState.Idle)
    val loginState: StateFlow<UiState<LoginData>> = _loginState

    fun login(email: String, password: String) {
        viewModelScope.launch {
            _loginState.value = UiState.Loading

            repository.login(email, password)
                .onSuccess { loginData ->
                    _loginState.value = UiState.Success(loginData)
                }
                .onFailure { exception ->
                    _loginState.value = UiState.Error(exception.message ?: "Unknown error")
                }
        }
    }
}

sealed class UiState<out T> {
    object Idle : UiState<Nothing>()
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val message: String) : UiState<Nothing>()
}
```

---

## 🍎 iOS Integration

### 1. إعداد Dependencies

#### Package.swift أو Xcode Package Manager
```swift
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
    .package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "5.0.0")
]
```

### 2. إنشاء Models

#### User.swift
```swift
import Foundation

struct User: Codable {
    let id: Int
    let name: String
    let email: String
    let phone: String?
    let type: String
    let isVerified: Bool
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id, name, email, phone, type
        case isVerified = "is_verified"
        case createdAt = "created_at"
    }
}
```

#### Product.swift
```swift
import Foundation

struct Product: Codable {
    let id: Int
    let title: String
    let description: String
    let price: String
    let categoryId: Int
    let city: String
    let location: String
    let status: String
    let isPromoted: Bool
    let images: [ProductImage]
    let category: Category?
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id, title, description, price, city, location, status, images, category
        case categoryId = "category_id"
        case isPromoted = "is_promoted"
        case createdAt = "created_at"
    }
}

struct ProductImage: Codable {
    let id: Int
    let imageUrl: String

    enum CodingKeys: String, CodingKey {
        case id
        case imageUrl = "image_url"
    }
}

struct Category: Codable {
    let id: Int
    let name: String
    let icon: String?
}
```

#### APIResponse.swift
```swift
import Foundation

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let message: String?
    let data: T?
    let errors: [String: [String]]?
}

struct LoginResponse: Codable {
    let success: Bool
    let message: String
    let data: LoginData
}

struct LoginData: Codable {
    let token: String
    let user: User
}
```

### 3. إنشاء API Manager

#### APIManager.swift
```swift
import Foundation
import Alamofire

class APIManager {
    static let shared = APIManager()
    private let baseURL = "https://yourdomain.com/api/"

    private init() {}

    private var headers: HTTPHeaders {
        var headers: HTTPHeaders = [
            "Accept": "application/json",
            "Content-Type": "application/json"
        ]

        if let token = TokenManager.shared.getToken() {
            headers["Authorization"] = "Bearer \(token)"
        }

        return headers
    }

    // MARK: - Authentication
    func login(email: String, password: String) async throws -> LoginData {
        let parameters: [String: Any] = [
            "email": email,
            "password": password
        ]

        let response = try await AF.request(
            "\(baseURL)login",
            method: .post,
            parameters: parameters,
            encoding: JSONEncoding.default,
            headers: headers
        ).serializingDecodable(LoginResponse.self).value

        if response.success {
            TokenManager.shared.saveToken(response.data.token)
            TokenManager.shared.saveUser(response.data.user)
            return response.data
        } else {
            throw APIError.loginFailed(response.message)
        }
    }
}
```

### 4. Token Manager

#### TokenManager.swift
```swift
import Foundation

class TokenManager {
    static let shared = TokenManager()
    private let userDefaults = UserDefaults.standard

    private let tokenKey = "auth_token"
    private let userKey = "user_data"

    private init() {}

    func saveToken(_ token: String) {
        userDefaults.set(token, forKey: tokenKey)
    }

    func getToken() -> String? {
        return userDefaults.string(forKey: tokenKey)
    }

    func saveUser(_ user: User) {
        if let encoded = try? JSONEncoder().encode(user) {
            userDefaults.set(encoded, forKey: userKey)
        }
    }

    func getUser() -> User? {
        if let data = userDefaults.data(forKey: userKey),
           let user = try? JSONDecoder().decode(User.self, from: data) {
            return user
        }
        return nil
    }

    func clearAll() {
        userDefaults.removeObject(forKey: tokenKey)
        userDefaults.removeObject(forKey: userKey)
    }

    func isLoggedIn() -> Bool {
        return getToken() != nil
    }
}
```

---

## 🎯 Flutter Integration

### 1. إعداد Dependencies

#### pubspec.yaml
```yaml
dependencies:
  flutter:
    sdk: flutter

  # HTTP client
  dio: ^5.3.2

  # State management
  provider: ^6.0.5

  # Local storage
  shared_preferences: ^2.2.2

  # JSON serialization
  json_annotation: ^4.8.1

  # Image handling
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # Push notifications
  firebase_messaging: ^14.7.6

dev_dependencies:
  # JSON code generation
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

### 2. إنشاء Models

#### user.dart
```dart
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String type;
  @JsonKey(name: 'is_verified')
  final bool isVerified;
  @JsonKey(name: 'created_at')
  final String createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.type,
    required this.isVerified,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

#### product.dart
```dart
import 'package:json_annotation/json_annotation.dart';

part 'product.g.dart';

@JsonSerializable()
class Product {
  final int id;
  final String title;
  final String description;
  final String price;
  @JsonKey(name: 'category_id')
  final int categoryId;
  final String city;
  final String location;
  final String status;
  @JsonKey(name: 'is_promoted')
  final bool isPromoted;
  final List<ProductImage> images;
  final Category? category;
  @JsonKey(name: 'created_at')
  final String createdAt;

  Product({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.categoryId,
    required this.city,
    required this.location,
    required this.status,
    required this.isPromoted,
    required this.images,
    this.category,
    required this.createdAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);
}

@JsonSerializable()
class ProductImage {
  final int id;
  @JsonKey(name: 'image_url')
  final String imageUrl;

  ProductImage({required this.id, required this.imageUrl});

  factory ProductImage.fromJson(Map<String, dynamic> json) => _$ProductImageFromJson(json);
  Map<String, dynamic> toJson() => _$ProductImageToJson(this);
}

@JsonSerializable()
class Category {
  final int id;
  final String name;
  final String? icon;

  Category({required this.id, required this.name, this.icon});

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);
}
```

#### api_response.dart
```dart
import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, List<String>>? errors;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

@JsonSerializable()
class LoginResponse {
  final bool success;
  final String message;
  final LoginData data;

  LoginResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class LoginData {
  final String token;
  final User user;

  LoginData({required this.token, required this.user});

  factory LoginData.fromJson(Map<String, dynamic> json) => _$LoginDataFromJson(json);
  Map<String, dynamic> toJson() => _$LoginDataToJson(this);
}
```

### 3. إنشاء API Service

#### api_service.dart
```dart
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'https://yourdomain.com/api/';
  late Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await TokenManager.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        _handleError(error);
        handler.next(error);
      },
    ));
  }

  // Authentication
  Future<LoginData> login(String email, String password) async {
    try {
      final response = await _dio.post('/login', data: {
        'email': email,
        'password': password,
      });

      final loginResponse = LoginResponse.fromJson(response.data);
      if (loginResponse.success) {
        await TokenManager.saveToken(loginResponse.data.token);
        await TokenManager.saveUser(loginResponse.data.user);
        return loginResponse.data;
      } else {
        throw ApiException(loginResponse.message);
      }
    } catch (e) {
      throw _handleException(e);
    }
  }

  Future<User> register({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String type,
  }) async {
    try {
      final response = await _dio.post('/register', data: {
        'name': name,
        'email': email,
        'phone': phone,
        'password': password,
        'password_confirmation': password,
        'type': type,
      });

      final apiResponse = ApiResponse<User>.fromJson(
        response.data,
        (json) => User.fromJson(json as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(apiResponse.message ?? 'Registration failed');
      }
    } catch (e) {
      throw _handleException(e);
    }
  }

  // Products
  Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await _dio.get('/products/featured');

      final apiResponse = ApiResponse<List<Product>>.fromJson(
        response.data,
        (json) => (json as List).map((item) => Product.fromJson(item)).toList(),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(apiResponse.message ?? 'Failed to fetch products');
      }
    } catch (e) {
      throw _handleException(e);
    }
  }

  // Categories
  Future<List<Category>> getCategories() async {
    try {
      final response = await _dio.get('/categories');

      final apiResponse = ApiResponse<List<Category>>.fromJson(
        response.data,
        (json) => (json as List).map((item) => Category.fromJson(item)).toList(),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(apiResponse.message ?? 'Failed to fetch categories');
      }
    } catch (e) {
      throw _handleException(e);
    }
  }

  void _handleError(DioException error) {
    print('API Error: ${error.message}');
    print('Status Code: ${error.response?.statusCode}');
    print('Response Data: ${error.response?.data}');
  }

  Exception _handleException(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiException('Connection timeout. Please check your internet connection.');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Server error occurred';
          return ApiException('Server error ($statusCode): $message');
        case DioExceptionType.cancel:
          return ApiException('Request was cancelled');
        default:
          return ApiException('Network error occurred');
      }
    }
    return ApiException(error.toString());
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);

  @override
  String toString() => message;
}
```

### 4. Token Manager

#### token_manager.dart
```dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class TokenManager {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  static Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(_userKey, userJson);
  }

  static Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);
    if (userJson != null) {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return User.fromJson(userMap);
    }
    return null;
  }

  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
  }

  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }
}
```

---

## ⚠️ معالجة الأخطاء

### 1. أنواع الأخطاء الشائعة

#### أخطاء الشبكة
```kotlin
// Android - Kotlin
sealed class NetworkError : Exception() {
    object NoInternet : NetworkError()
    object Timeout : NetworkError()
    object ServerError : NetworkError()
    data class HttpError(val code: Int, val message: String) : NetworkError()
}

fun handleNetworkError(error: NetworkError): String {
    return when (error) {
        is NetworkError.NoInternet -> "لا يوجد اتصال بالإنترنت"
        is NetworkError.Timeout -> "انتهت مهلة الاتصال"
        is NetworkError.ServerError -> "خطأ في الخادم"
        is NetworkError.HttpError -> "خطأ ${error.code}: ${error.message}"
    }
}
```

```swift
// iOS - Swift
enum NetworkError: Error, LocalizedError {
    case noInternet
    case timeout
    case serverError
    case httpError(code: Int, message: String)

    var errorDescription: String? {
        switch self {
        case .noInternet:
            return "لا يوجد اتصال بالإنترنت"
        case .timeout:
            return "انتهت مهلة الاتصال"
        case .serverError:
            return "خطأ في الخادم"
        case .httpError(let code, let message):
            return "خطأ \(code): \(message)"
        }
    }
}
```

```dart
// Flutter - Dart
enum ErrorType {
  network,
  server,
  validation,
  authentication,
  unknown,
}

class AppError {
  final ErrorType type;
  final String message;
  final int? statusCode;

  AppError({
    required this.type,
    required this.message,
    this.statusCode,
  });

  static AppError fromException(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
          return AppError(
            type: ErrorType.network,
            message: 'انتهت مهلة الاتصال',
          );
        case DioExceptionType.badResponse:
          return AppError(
            type: ErrorType.server,
            message: error.response?.data?['message'] ?? 'خطأ في الخادم',
            statusCode: error.response?.statusCode,
          );
        default:
          return AppError(
            type: ErrorType.network,
            message: 'خطأ في الشبكة',
          );
      }
    }
    return AppError(
      type: ErrorType.unknown,
      message: error.toString(),
    );
  }
}
```

### 2. معالجة أخطاء المصادقة

#### Token Expiration Handling
```kotlin
// Android - Kotlin
class TokenInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        if (response.code == 401) {
            // Token expired, redirect to login
            TokenManager.clearAll()
            // Navigate to login screen
            navigateToLogin()
        }

        return response
    }
}
```

```swift
// iOS - Swift
extension APIManager {
    private func handleUnauthorized() {
        TokenManager.shared.clearAll()
        DispatchQueue.main.async {
            // Navigate to login screen
            self.navigateToLogin()
        }
    }
}
```

```dart
// Flutter - Dart
class AuthInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired
      TokenManager.clearAll();
      // Navigate to login
      navigateToLogin();
    }
    handler.next(err);
  }
}
```

### 3. Retry Logic

#### Automatic Retry for Failed Requests
```kotlin
// Android - Kotlin
class RetryInterceptor(private val maxRetries: Int = 3) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        var response: Response? = null
        var exception: IOException? = null

        for (i in 0..maxRetries) {
            try {
                response = chain.proceed(chain.request())
                if (response.isSuccessful) {
                    return response
                }
            } catch (e: IOException) {
                exception = e
                if (i == maxRetries) {
                    throw e
                }
                // Wait before retry
                Thread.sleep(1000 * (i + 1))
            }
        }

        return response ?: throw exception!!
    }
}
```

---

## 🔒 أفضل الممارسات

### 1. أمان البيانات

#### تشفير البيانات المحلية
```kotlin
// Android - Kotlin
class SecureTokenManager(private val context: Context) {
    private val keyAlias = "mega_platform_key"

    fun saveTokenSecurely(token: String) {
        val encryptedPrefs = EncryptedSharedPreferences.create(
            "secure_prefs",
            keyAlias,
            context,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )

        encryptedPrefs.edit()
            .putString("auth_token", token)
            .apply()
    }
}
```

#### Certificate Pinning
```kotlin
// Android - Kotlin
val certificatePinner = CertificatePinner.Builder()
    .add("yourdomain.com", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
    .build()

val okHttpClient = OkHttpClient.Builder()
    .certificatePinner(certificatePinner)
    .build()
```

### 2. إدارة الحالة

#### State Management Pattern
```dart
// Flutter - Provider Pattern
class AuthProvider extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;

  Future<void> login(String email, String password) async {
    _setLoading(true);
    _setError(null);

    try {
      final loginData = await ApiService().login(email, password);
      _user = loginData.user;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
```

### 3. Caching Strategy

#### Image Caching
```dart
// Flutter - Cached Network Image
CachedNetworkImage(
  imageUrl: product.images.first.imageUrl,
  placeholder: (context, url) => CircularProgressIndicator(),
  errorWidget: (context, url, error) => Icon(Icons.error),
  cacheManager: CacheManager(
    Config(
      'mega_platform_cache',
      stalePeriod: Duration(days: 7),
      maxNrOfCacheObjects: 100,
    ),
  ),
)
```

#### Data Caching
```kotlin
// Android - Room Database for Offline Support
@Entity(tableName = "cached_products")
data class CachedProduct(
    @PrimaryKey val id: Int,
    val title: String,
    val description: String,
    val price: String,
    val categoryId: Int,
    val cachedAt: Long = System.currentTimeMillis()
)

@Dao
interface ProductDao {
    @Query("SELECT * FROM cached_products WHERE cachedAt > :timestamp")
    suspend fun getCachedProducts(timestamp: Long): List<CachedProduct>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun cacheProducts(products: List<CachedProduct>)
}
```

### 4. Performance Optimization

#### Image Optimization
```kotlin
// Android - Glide with optimization
Glide.with(context)
    .load(imageUrl)
    .override(300, 300) // Resize for mobile
    .centerCrop()
    .diskCacheStrategy(DiskCacheStrategy.ALL)
    .into(imageView)
```

#### Pagination Implementation
```dart
// Flutter - Infinite Scroll
class ProductListWidget extends StatefulWidget {
  @override
  _ProductListWidgetState createState() => _ProductListWidgetState();
}

class _ProductListWidgetState extends State<ProductListWidget> {
  final ScrollController _scrollController = ScrollController();
  List<Product> _products = [];
  bool _isLoading = false;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadProducts();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreProducts();
    }
  }

  Future<void> _loadProducts() async {
    setState(() => _isLoading = true);

    try {
      final products = await ApiService().getProducts(page: _currentPage);
      setState(() {
        _products.addAll(products);
        _currentPage++;
      });
    } catch (e) {
      // Handle error
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
```

### 5. Testing

#### Unit Testing Example
```dart
// Flutter - Unit Test
void main() {
  group('ApiService Tests', () {
    late ApiService apiService;
    late MockDio mockDio;

    setUp(() {
      mockDio = MockDio();
      apiService = ApiService(dio: mockDio);
    });

    test('login should return LoginData on success', () async {
      // Arrange
      final responseData = {
        'success': true,
        'data': {
          'token': 'test_token',
          'user': {
            'id': 1,
            'name': 'Test User',
            'email': '<EMAIL>',
            'type': 'user',
            'is_verified': true,
            'created_at': '2025-01-12T10:30:00.000000Z'
          }
        }
      };

      when(mockDio.post('/login', data: anyNamed('data')))
          .thenAnswer((_) async => Response(
                data: responseData,
                statusCode: 200,
                requestOptions: RequestOptions(path: '/login'),
              ));

      // Act
      final result = await apiService.login('<EMAIL>', 'password');

      // Assert
      expect(result.token, 'test_token');
      expect(result.user.name, 'Test User');
    });
  });
}
```

---

## 📚 مراجع مفيدة

### Documentation Links
- **Laravel Sanctum**: https://laravel.com/docs/sanctum
- **Retrofit (Android)**: https://square.github.io/retrofit/
- **Alamofire (iOS)**: https://github.com/Alamofire/Alamofire
- **Dio (Flutter)**: https://pub.dev/packages/dio

### Best Practices
- **RESTful API Design**: https://restfulapi.net/
- **Mobile Security**: https://owasp.org/www-project-mobile-security/
- **Performance Optimization**: Platform-specific guidelines

---

*آخر تحديث: يناير 2025*
```
