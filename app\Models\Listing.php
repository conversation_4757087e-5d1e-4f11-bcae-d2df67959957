<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory; 


class Listing extends Model
{
        use HasFactory;
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'category_id',
        'country',
        'city',
        'location',
        'price',
        'is_promoted',
        'status',
        'rating_avg',
        'rating_count',
    ];

    protected $casts = [
        'promoted_until' => 'datetime',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(ListingImage::class);
    }

   

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function isApproved()
    {
        return $this->status === 'approved';
    }

    public function incrementViews()
    {
        $this->increment('views');
    }
    

public function scopeFilter($query, array $filters)
{
    $query->when($filters['status'] ?? false, fn($query, $status) =>
        $query->where('status', $status)
    );
    
    $query->when($filters['category'] ?? false, fn($query, $categoryId) =>
        $query->where('category_id', $categoryId)
    );
    
    $query->when($filters['search'] ?? false, fn($query, $search) =>
        $query->where('title', 'like', '%'.$search.'%')
    );
}
public function reports()
{
    return $this->hasMany(\App\Models\Report::class);
}

public function firstImage()
{
    return $this->hasOne(ListingImage::class)->latestOfMany();
}

public function ratings()
{
    return $this->hasMany(Rating::class, 'listing_id');
}

public function wishlists()
{
    return $this->hasMany(Wishlist::class);
}

public function wishlistedBy()
{
    return $this->belongsToMany(User::class, 'wishlists', 'listing_id', 'user_id')
                ->withTimestamps();
}

public function scopeActive($query)
{
    return $query->where('status', 'approved');
}

}