<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wishlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'listing_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * علاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * علاقة مع المنتج
     */
    public function listing()
    {
        return $this->belongsTo(Listing::class);
    }

    /**
     * Scope للحصول على wishlist مستخدم معين
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope للحصول على wishlist مع تفاصيل المنتجات
     */
    public function scopeWithListingDetails($query)
    {
        return $query->with([
            'listing' => function ($query) {
                $query->select('id', 'title', 'price', 'description', 'city', 'status', 'rating_avg', 'rating_count', 'user_id', 'category_id')
                      ->with([
                          'images:id,listing_id,image_url',
                          'category:id,name',
                          'user:id,name,phone'
                      ]);
            }
        ]);
    }

    /**
     * التحقق من وجود منتج في wishlist المستخدم
     */
    public static function isInWishlist($userId, $listingId)
    {
        return self::where('user_id', $userId)
                   ->where('listing_id', $listingId)
                   ->exists();
    }

    /**
     * إضافة منتج لـ wishlist
     */
    public static function addToWishlist($userId, $listingId)
    {
        return self::firstOrCreate([
            'user_id' => $userId,
            'listing_id' => $listingId,
        ]);
    }

    /**
     * حذف منتج من wishlist
     */
    public static function removeFromWishlist($userId, $listingId)
    {
        return self::where('user_id', $userId)
                   ->where('listing_id', $listingId)
                   ->delete();
    }
}
