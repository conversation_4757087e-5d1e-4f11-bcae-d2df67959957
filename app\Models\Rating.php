<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory; 
class Rating extends Model
{
    use HasFactory;
    protected $fillable = [
        'reviewer_id',
        'reviewed_id',
        'listing_id',
        'order_id',
        'rating',
        'comment',
    ];

    public function listing()
    {
        return $this->belongsTo(Listing::class);
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    public function reviewed()
    {
        return $this->belongsTo(User::class, 'reviewed_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
   

    // دالة للحصول على النجوم كأيقونات
    public function getStarsAttribute()
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $this->rating 
                ? '<i class="fas fa-star text-yellow-400"></i>' 
                : '<i class="far fa-star text-yellow-400"></i>';
        }
        return $stars;
    }
}