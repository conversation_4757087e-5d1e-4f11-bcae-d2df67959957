<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\RatingController;
use App\Http\Controllers\Api\WishlistController;
use App\Http\Controllers\ListingController;
use App\Http\Controllers\CategoryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

 Route::get('/networkCatAPI', [CategoryController::class, 'networkCatAPI']);
Route::get('categories', [CategoryController::class, 'getCategories']);
Route::get('products/featured', [ListingController::class, 'getFeaturedProducts']);
Route::get('/categories/{category}/listings', [CategoryController::class, 'relatedProducts']);

// Auth Routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected Routes (تتطلب Token مصادقة)
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    // Products APIs
    Route::get('provider/products', [\App\Http\Controllers\ListingController::class, 'providerProducts']);
    Route::post('provider/products', [\App\Http\Controllers\ListingController::class, 'storeAPI']);
    Route::delete('provider/products/{id}', [\App\Http\Controllers\ListingController::class, 'destroyApi']);

    // Orders APIs
    Route::get('orders', [OrderController::class, 'index']);
    Route::post('orders', [OrderController::class, 'store']);
    Route::get('orders/{id}', [OrderController::class, 'show']);
    Route::put('orders/{id}/cancel', [OrderController::class, 'cancel']);
    Route::get('orders/stats/user', [OrderController::class, 'userStats']);

    // Provider Orders APIs
    Route::get('provider/orders', [OrderController::class, 'providerOrders']);
    Route::put('provider/orders/{id}/accept', [OrderController::class, 'accept']);
    Route::put('provider/orders/{id}/reject', [OrderController::class, 'reject']);
    Route::put('provider/orders/{id}/delivered', [OrderController::class, 'markAsDelivered']);
    Route::get('provider/orders/stats', [OrderController::class, 'providerStats']);

    // Ratings APIs
    Route::post('ratings', [RatingController::class, 'store']);
    Route::put('ratings/{id}', [RatingController::class, 'update']);
    Route::delete('ratings/{id}', [RatingController::class, 'destroy']);
    Route::get('ratings/user', [RatingController::class, 'userRatings']);
    Route::get('ratings/provider', [RatingController::class, 'providerRatings']);

    // Wishlist APIs
    Route::get('wishlist', [WishlistController::class, 'index']);
    Route::post('wishlist', [WishlistController::class, 'store']);
    Route::delete('wishlist/{listingId}', [WishlistController::class, 'destroy']);
    Route::get('wishlist/check/{listingId}', [WishlistController::class, 'check']);
    Route::get('wishlist/stats', [WishlistController::class, 'stats']);
    Route::delete('wishlist', [WishlistController::class, 'clear']);
    Route::post('wishlist/move-to-cart', [WishlistController::class, 'moveToCart']);
});

// Public Ratings APIs (لا تحتاج مصادقة)
Route::get('products/{productId}/ratings', [RatingController::class, 'getProductRatings']);

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// ⚠️ SECURITY FIX: Removed dangerous /destroy-project endpoint
// This endpoint was capable of deleting the entire project
// Removed on: January 12, 2025
// Reason: Critical security vulnerability
