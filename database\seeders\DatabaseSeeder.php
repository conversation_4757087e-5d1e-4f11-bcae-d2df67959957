<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Listing;
use App\Models\ListingImage;
use App\Models\Order;
use App\Models\Rating;
use App\Models\DigitalCard;
use App\Models\AdTargeting;
use App\Models\Setting;


class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // ✅ Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'phone' => '**********',
                'password' => bcrypt('password'),
                'type' => 'admin',
                'is_verified' => true,
            ]
        );

        // ✅ Create categories and store them
        $categories = Category::factory()->count(10)->create();

        // ✅ Create providers and their listings
        User::factory()->count(5)->create(['type' => 'provider', 'is_verified' => true])
            ->each(function ($user) use ($categories) {
                Listing::factory()->count(rand(3, 10))->create([
                    'user_id' => $user->id,
                    'category_id' => $categories->random()->id,
                ])->each(function ($listing) {
                    ListingImage::factory()->count(rand(1, 5))->create([
                        'listing_id' => $listing->id,
                    ]);
                });
            });

        // ✅ Create regular users with interests
        User::factory()->count(20)->create(['type' => 'user', 'is_verified' => true])
            ->each(function ($user) use ($categories) {
                $interested = $categories->random(rand(1, 3));
                foreach ($interested as $category) {
                    AdTargeting::create([
                        'user_id' => $user->id,
                        'category_id' => $category->id,
                    ]);
                }
            });



        // ✅ Create orders
        Order::factory()->count(50)->create();

        // ✅ Create ratings
        Rating::factory()->count(30)->create();

        // ✅ Create digital cards
        DigitalCard::factory()->count(15)->create();

        // ✅ Create settings
        Setting::insert([
            ['key' => 'site_name', 'value' => 'Mega Store', 'created_at' => now(), 'updated_at' => now()],
            ['key' => 'site_email', 'value' => '<EMAIL>', 'created_at' => now(), 'updated_at' => now()],
            ['key' => 'commission_rate', 'value' => '10', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
