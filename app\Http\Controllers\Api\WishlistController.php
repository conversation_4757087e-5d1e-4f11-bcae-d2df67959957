<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Wishlist;
use App\Models\Listing;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class WishlistController extends Controller
{
    /**
     * عرض قائمة الأمنيات للمستخدم
     */
    public function index(Request $request)
    {
        $query = Wishlist::forUser(Auth::id())
            ->withListingDetails()
            ->latest();

        // فلترة المنتجات المتاحة فقط
        if ($request->get('available_only', true)) {
            $query->whereHas('listing', function ($q) {
                $q->where('status', 'approved');
            });
        }

        $wishlist = $query->paginate(15);

        // تحويل البيانات لتنسيق مناسب للـ API
        $formattedWishlist = $wishlist->through(function ($item) {
            return [
                'id' => $item->id,
                'added_at' => $item->created_at,
                'product' => [
                    'id' => $item->listing->id,
                    'title' => $item->listing->title,
                    'price' => $item->listing->price,
                    'description' => $item->listing->description,
                    'city' => $item->listing->city,
                    'status' => $item->listing->status,
                    'rating_avg' => $item->listing->rating_avg,
                    'rating_count' => $item->listing->rating_count,
                    'category' => [
                        'id' => $item->listing->category->id,
                        'name' => $item->listing->category->name,
                    ],
                    'provider' => [
                        'id' => $item->listing->user->id,
                        'name' => $item->listing->user->name,
                        'phone' => $item->listing->user->phone,
                    ],
                    'images' => $item->listing->images->map(function ($image) {
                        return [
                            'id' => $image->id,
                            'image_url' => url('storage/' . $image->image_url)
                        ];
                    })->toArray(),
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedWishlist->items(),
            'meta' => [
                'current_page' => $formattedWishlist->currentPage(),
                'total' => $formattedWishlist->total(),
                'per_page' => $formattedWishlist->perPage(),
                'last_page' => $formattedWishlist->lastPage(),
            ]
        ]);
    }

    /**
     * إضافة منتج إلى قائمة الأمنيات
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'listing_id' => 'required|exists:listings,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $listing = Listing::findOrFail($request->listing_id);

        // التحقق من أن المنتج معتمد
        if ($listing->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'هذا المنتج غير متاح حالياً'
            ], 400);
        }

        // التحقق من أن المستخدم لا يضيف منتجه الخاص
        if ($listing->user_id === Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك إضافة منتجك الخاص إلى قائمة الأمنيات'
            ], 400);
        }

        // التحقق من عدم وجود المنتج في القائمة مسبقاً
        if (Wishlist::isInWishlist(Auth::id(), $request->listing_id)) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المنتج موجود في قائمة الأمنيات مسبقاً'
            ], 400);
        }

        // إضافة المنتج إلى قائمة الأمنيات
        $wishlistItem = Wishlist::addToWishlist(Auth::id(), $request->listing_id);

        // تحميل تفاصيل المنتج
        $wishlistItem->load(['listing.images', 'listing.category', 'listing.user:id,name,phone']);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة المنتج إلى قائمة الأمنيات بنجاح',
            'data' => [
                'id' => $wishlistItem->id,
                'added_at' => $wishlistItem->created_at,
                'product' => [
                    'id' => $wishlistItem->listing->id,
                    'title' => $wishlistItem->listing->title,
                    'price' => $wishlistItem->listing->price,
                ]
            ]
        ], 201);
    }

    /**
     * حذف منتج من قائمة الأمنيات
     */
    public function destroy($listingId)
    {
        $deleted = Wishlist::removeFromWishlist(Auth::id(), $listingId);

        if (!$deleted) {
            return response()->json([
                'success' => false,
                'message' => 'المنتج غير موجود في قائمة الأمنيات'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المنتج من قائمة الأمنيات بنجاح'
        ]);
    }

    /**
     * التحقق من وجود منتج في قائمة الأمنيات
     */
    public function check($listingId)
    {
        $isInWishlist = Wishlist::isInWishlist(Auth::id(), $listingId);

        return response()->json([
            'success' => true,
            'data' => [
                'is_in_wishlist' => $isInWishlist
            ]
        ]);
    }

    /**
     * إحصائيات قائمة الأمنيات
     */
    public function stats()
    {
        $userId = Auth::id();
        
        $totalItems = Wishlist::forUser($userId)->count();
        $availableItems = Wishlist::forUser($userId)
            ->whereHas('listing', function ($q) {
                $q->where('status', 'approved');
            })->count();
        
        $categoriesCount = Wishlist::where('wishlists.user_id', $userId)
            ->join('listings', 'wishlists.listing_id', '=', 'listings.id')
            ->join('categories', 'listings.category_id', '=', 'categories.id')
            ->where('listings.status', 'approved')
            ->distinct('categories.id')
            ->count('categories.id');

        $recentlyAdded = Wishlist::forUser($userId)
            ->withListingDetails()
            ->whereHas('listing', function ($q) {
                $q->where('status', 'approved');
            })
            ->latest()
            ->take(3)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->listing->id,
                    'title' => $item->listing->title,
                    'price' => $item->listing->price,
                    'added_at' => $item->created_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'total_items' => $totalItems,
                'available_items' => $availableItems,
                'categories_count' => $categoriesCount,
                'recently_added' => $recentlyAdded,
            ]
        ]);
    }

    /**
     * مسح قائمة الأمنيات بالكامل
     */
    public function clear()
    {
        $deletedCount = Wishlist::forUser(Auth::id())->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$deletedCount} منتج من قائمة الأمنيات",
            'data' => [
                'deleted_count' => $deletedCount
            ]
        ]);
    }

    /**
     * نقل منتجات من قائمة الأمنيات إلى الطلبات
     */
    public function moveToCart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'listing_ids' => 'required|array|min:1',
            'listing_ids.*' => 'exists:listings,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $userId = Auth::id();
        $movedItems = [];

        foreach ($request->listing_ids as $listingId) {
            // التحقق من وجود المنتج في قائمة الأمنيات
            if (Wishlist::isInWishlist($userId, $listingId)) {
                // هنا يمكن إضافة منطق نقل إلى سلة التسوق
                // حالياً سنحذف من قائمة الأمنيات فقط
                Wishlist::removeFromWishlist($userId, $listingId);
                $movedItems[] = $listingId;
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'تم نقل المنتجات المحددة',
            'data' => [
                'moved_items' => $movedItems,
                'moved_count' => count($movedItems)
            ]
        ]);
    }
}
