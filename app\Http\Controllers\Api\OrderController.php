<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Listing;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * عرض قائمة الطلبات للمستخدم الحالي
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Order::with(['listing.images', 'listing.category', 'provider:id,name,phone'])
            ->where('user_id', $user->id)
            ->latest();

        // فلترة حسب الحالة إذا تم تمريرها
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $orders = $query->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders->items(),
            'meta' => [
                'current_page' => $orders->currentPage(),
                'total' => $orders->total(),
                'per_page' => $orders->perPage(),
                'last_page' => $orders->lastPage(),
            ]
        ]);
    }

    /**
     * إنشاء طلب جديد
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'listing_id' => 'required|exists:listings,id',
            'delivery_address' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $listing = Listing::with('user')->findOrFail($request->listing_id);
        
        // التأكد من أن المنتج معتمد
        if ($listing->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'هذا المنتج غير متاح للطلب حالياً'
            ], 400);
        }

        // التأكد من أن المستخدم لا يطلب منتجه الخاص
        if ($listing->user_id === Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك طلب منتجك الخاص'
            ], 400);
        }

        $order = Order::create([
            'listing_id' => $request->listing_id,
            'user_id' => Auth::id(),
            'provider_id' => $listing->user_id,
            'delivery_address' => $request->delivery_address,
            'status' => 'pending',
        ]);

        $order->load(['listing.images', 'listing.category', 'provider:id,name,phone']);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الطلب بنجاح',
            'data' => $order
        ], 201);
    }

    /**
     * عرض تفاصيل طلب محدد
     */
    public function show($id)
    {
        $order = Order::with(['listing.images', 'listing.category', 'provider:id,name,phone'])
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $order
        ]);
    }

    /**
     * إلغاء طلب (للمستخدم فقط)
     */
    public function cancel($id)
    {
        $order = Order::where('user_id', Auth::id())
            ->where('status', 'pending')
            ->findOrFail($id);

        $order->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء الطلب بنجاح'
        ]);
    }

    /**
     * قائمة الطلبات للبائع (Provider)
     */
    public function providerOrders(Request $request)
    {
        $user = Auth::user();
        
        if ($user->type !== 'provider') {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بالوصول'
            ], 403);
        }

        $query = Order::with(['listing.images', 'user:id,name,phone'])
            ->where('provider_id', $user->id)
            ->latest();

        // فلترة حسب الحالة
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $orders = $query->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders->items(),
            'meta' => [
                'current_page' => $orders->currentPage(),
                'total' => $orders->total(),
                'per_page' => $orders->perPage(),
                'last_page' => $orders->lastPage(),
            ]
        ]);
    }

    /**
     * قبول طلب (للبائع فقط)
     */
    public function accept($id)
    {
        $order = Order::where('provider_id', Auth::id())
            ->where('status', 'pending')
            ->findOrFail($id);

        $order->update(['status' => 'accepted']);

        return response()->json([
            'success' => true,
            'message' => 'تم قبول الطلب بنجاح'
        ]);
    }

    /**
     * تأكيد التسليم (للبائع فقط)
     */
    public function markAsDelivered($id)
    {
        $order = Order::where('provider_id', Auth::id())
            ->where('status', 'accepted')
            ->findOrFail($id);

        $order->update(['status' => 'delivered']);

        return response()->json([
            'success' => true,
            'message' => 'تم تأكيد التسليم بنجاح'
        ]);
    }

    /**
     * رفض طلب (للبائع فقط)
     */
    public function reject($id)
    {
        $order = Order::where('provider_id', Auth::id())
            ->where('status', 'pending')
            ->findOrFail($id);

        $order->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'تم رفض الطلب'
        ]);
    }

    /**
     * إحصائيات الطلبات للمستخدم
     */
    public function userStats()
    {
        $userId = Auth::id();
        
        $stats = [
            'total_orders' => Order::where('user_id', $userId)->count(),
            'pending_orders' => Order::where('user_id', $userId)->where('status', 'pending')->count(),
            'accepted_orders' => Order::where('user_id', $userId)->where('status', 'accepted')->count(),
            'delivered_orders' => Order::where('user_id', $userId)->where('status', 'delivered')->count(),
            'cancelled_orders' => Order::where('user_id', $userId)->where('status', 'cancelled')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * إحصائيات الطلبات للبائع
     */
    public function providerStats()
    {
        $providerId = Auth::id();
        
        $stats = [
            'total_orders' => Order::where('provider_id', $providerId)->count(),
            'pending_orders' => Order::where('provider_id', $providerId)->where('status', 'pending')->count(),
            'accepted_orders' => Order::where('provider_id', $providerId)->where('status', 'accepted')->count(),
            'delivered_orders' => Order::where('provider_id', $providerId)->where('status', 'delivered')->count(),
            'cancelled_orders' => Order::where('provider_id', $providerId)->where('status', 'cancelled')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
