<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Rating;
use App\Models\Order;
use App\Models\Listing;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class RatingController extends Controller
{
    /**
     * عرض تقييمات منتج معين
     */
    public function getProductRatings($productId)
    {
        $ratings = Rating::with(['reviewer:id,name', 'order:id,created_at'])
            ->where('listing_id', $productId)
            ->latest()
            ->paginate(15);

        $averageRating = Rating::where('listing_id', $productId)->avg('rating');
        $totalRatings = Rating::where('listing_id', $productId)->count();

        return response()->json([
            'success' => true,
            'data' => [
                'ratings' => $ratings->items(),
                'average_rating' => round($averageRating, 1),
                'total_ratings' => $totalRatings,
                'rating_breakdown' => $this->getRatingBreakdown($productId)
            ],
            'meta' => [
                'current_page' => $ratings->currentPage(),
                'total' => $ratings->total(),
                'per_page' => $ratings->perPage(),
                'last_page' => $ratings->lastPage(),
            ]
        ]);
    }

    /**
     * إضافة تقييم جديد
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $order = Order::with('listing')->findOrFail($request->order_id);

        // التحقق من أن الطلب مكتمل
        if ($order->status !== 'delivered') {
            return response()->json([
                'success' => false,
                'message' => 'يمكن تقييم الطلبات المكتملة فقط'
            ], 400);
        }

        // التحقق من أن المستخدم هو صاحب الطلب
        if ($order->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بتقييم هذا الطلب'
            ], 403);
        }

        // التحقق من عدم وجود تقييم سابق
        $existingRating = Rating::where('order_id', $request->order_id)
            ->where('reviewer_id', Auth::id())
            ->first();

        if ($existingRating) {
            return response()->json([
                'success' => false,
                'message' => 'لقد قمت بتقييم هذا الطلب مسبقاً'
            ], 400);
        }

        DB::beginTransaction();
        try {
            // إنشاء التقييم
            $rating = Rating::create([
                'reviewer_id' => Auth::id(),
                'reviewed_id' => $order->provider_id,
                'listing_id' => $order->listing_id,
                'order_id' => $request->order_id,
                'rating' => $request->rating,
                'comment' => $request->comment,
            ]);

            // تحديث متوسط التقييم للمنتج
            $this->updateListingRating($order->listing_id);

            // تحديث متوسط التقييم للبائع
            $this->updateProviderRating($order->provider_id);

            DB::commit();

            $rating->load(['reviewer:id,name', 'order:id,created_at']);

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة التقييم بنجاح',
                'data' => $rating
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة التقييم'
            ], 500);
        }
    }

    /**
     * تحديث تقييم موجود
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $rating = Rating::where('reviewer_id', Auth::id())->findOrFail($id);

        DB::beginTransaction();
        try {
            $rating->update([
                'rating' => $request->rating,
                'comment' => $request->comment,
            ]);

            // تحديث متوسط التقييم للمنتج
            $this->updateListingRating($rating->listing_id);

            // تحديث متوسط التقييم للبائع
            $this->updateProviderRating($rating->reviewed_id);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث التقييم بنجاح',
                'data' => $rating
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث التقييم'
            ], 500);
        }
    }

    /**
     * حذف تقييم
     */
    public function destroy($id)
    {
        $rating = Rating::where('reviewer_id', Auth::id())->findOrFail($id);

        DB::beginTransaction();
        try {
            $listingId = $rating->listing_id;
            $providerId = $rating->reviewed_id;

            $rating->delete();

            // تحديث متوسط التقييم للمنتج
            $this->updateListingRating($listingId);

            // تحديث متوسط التقييم للبائع
            $this->updateProviderRating($providerId);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف التقييم بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف التقييم'
            ], 500);
        }
    }

    /**
     * عرض تقييمات المستخدم
     */
    public function userRatings()
    {
        $ratings = Rating::with(['listing:id,title', 'reviewed:id,name'])
            ->where('reviewer_id', Auth::id())
            ->latest()
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $ratings->items(),
            'meta' => [
                'current_page' => $ratings->currentPage(),
                'total' => $ratings->total(),
                'per_page' => $ratings->perPage(),
                'last_page' => $ratings->lastPage(),
            ]
        ]);
    }

    /**
     * عرض التقييمات المستلمة للبائع
     */
    public function providerRatings()
    {
        $ratings = Rating::with(['reviewer:id,name', 'listing:id,title'])
            ->where('reviewed_id', Auth::id())
            ->latest()
            ->paginate(15);

        $averageRating = Rating::where('reviewed_id', Auth::id())->avg('rating');
        $totalRatings = Rating::where('reviewed_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'data' => [
                'ratings' => $ratings->items(),
                'average_rating' => round($averageRating, 1),
                'total_ratings' => $totalRatings,
            ],
            'meta' => [
                'current_page' => $ratings->currentPage(),
                'total' => $ratings->total(),
                'per_page' => $ratings->perPage(),
                'last_page' => $ratings->lastPage(),
            ]
        ]);
    }

    /**
     * تحديث متوسط التقييم للمنتج
     */
    private function updateListingRating($listingId)
    {
        $averageRating = Rating::where('listing_id', $listingId)->avg('rating');
        $totalRatings = Rating::where('listing_id', $listingId)->count();

        Listing::where('id', $listingId)->update([
            'rating_avg' => $averageRating ? round($averageRating, 2) : 0,
            'rating_count' => $totalRatings
        ]);
    }

    /**
     * تحديث متوسط التقييم للبائع
     */
    private function updateProviderRating($providerId)
    {
        $averageRating = Rating::where('reviewed_id', $providerId)->avg('rating');
        $totalRatings = Rating::where('reviewed_id', $providerId)->count();

        User::where('id', $providerId)->update([
            'rating_avg' => $averageRating ? round($averageRating, 2) : 0,
            'rating_count' => $totalRatings
        ]);
    }

    /**
     * تفصيل التقييمات (1-5 نجوم)
     */
    private function getRatingBreakdown($productId)
    {
        $breakdown = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = Rating::where('listing_id', $productId)
                ->where('rating', $i)
                ->count();
            $breakdown[$i] = $count;
        }
        return $breakdown;
    }
}
