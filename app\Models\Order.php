<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'listing_id',
        'user_id',
        'provider_id',
        'status',
        'delivery_address',
    ];

    // Relationships
    public function listing()
    {
        return $this->belongsTo(Listing::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }



    public function transaction()
    {
        return $this->hasOne(Transaction::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    // Helper methods
    public function markAsAccepted()
    {
        $this->update(['status' => 'accepted']);
    }

    public function markAsDelivered()
    {
        $this->update(['status' => 'delivered']);
    }
}