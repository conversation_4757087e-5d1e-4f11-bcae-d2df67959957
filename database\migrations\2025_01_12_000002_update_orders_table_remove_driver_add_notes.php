<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            // حذف عمود driver_id
            $table->dropForeign(['driver_id']);
            $table->dropColumn('driver_id');
            
            // إضافة عمود notes
            $table->text('notes')->nullable()->after('delivery_address');
            
            // تحديث enum للحالات
            $table->dropColumn('status');
        });
        
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'accepted', 'delivered', 'cancelled'])->default('pending')->after('provider_id');
        });
    }

    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            // إعادة driver_id
            $table->foreignId('driver_id')->nullable()->constrained('users')->onDelete('set null')->after('provider_id');
            
            // حذف notes
            $table->dropColumn('notes');
            
            // إعادة enum القديم
            $table->dropColumn('status');
        });
        
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'accepted', 'delivered'])->default('pending')->after('driver_id');
        });
    }
};
