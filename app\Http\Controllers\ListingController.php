<?php

namespace App\Http\Controllers;

use App\Models\Listing;
use App\Models\ListingImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ListingController extends Controller
{
    public function create()
    {
        $categories = \App\Models\Category::all();
        return view('provider.listings.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'city' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'images.*' => 'required|image|mimes:jpeg,png,jpg|max:5120',
        ]);
        
        $listing = Listing::create([
            'title' => $request->title,
            'description' => $request->description,
            'price' => $request->price,
            'category_id' => $request->category_id,
            'country' => 'سوريا',
            'city' => $request->city,
            'location' => $request->location,
            'user_id' => Auth::id(),
            'is_promoted' => false,
            'status' => 'pending',
        ]);

        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('listings', 'public');
                
                ListingImage::create([
                    'listing_id' => $listing->id,
                    'image_url' => $path,
                ]);
            }
        }

        return redirect()->route('provider.listings.index')
                         ->with('success', 'تم إضافة الإعلان بنجاح وهو قيد المراجعة');
    }
  public function providerIndex(Request $request)
{
    $status = $request->query('status');
    
    $query = Listing::where('user_id', auth()->id())
        ->with(['category', 'images'])
        ->latest();
    
    if ($status) {
        $query->where('status', $status);
    }
    
    $listings = $query->paginate(10);
    
    $stats = [
        'totalListings' => Listing::where('user_id', auth()->id())->count(),
        'publishedListings' => Listing::where('user_id', auth()->id())->where('status', 'approved')->count(),
        'pendingListings' => Listing::where('user_id', auth()->id())->where('status', 'pending')->count(),
        'rejectedListings' => Listing::where('user_id', auth()->id())->where('status', 'rejected')->count(),
    ];
    
    return view('provider.listings.index', compact('listings', 'stats'));
}


public function destroy($id)
{
    $listing = Listing::where('id', $id)
                      ->where('user_id', auth()->id()) // لضمان الحماية
                      ->firstOrFail();

    // حذف الصور من التخزين
    foreach ($listing->images as $image) {
        Storage::disk('public')->delete($image->image_url);
        $image->delete();
    }

    $listing->delete();

    return redirect()->route('provider.listings.index')
                     ->with('success', 'تم حذف الإعلان بنجاح');
}
    public function show(Listing $listing)
    {
        // Get related listings from the same category
        $relatedListings = Listing::where('category_id', $listing->category_id)
                                ->where('id', '!=', $listing->id)
                                ->inRandomOrder()
                                ->limit(4)
                                ->get();

        return view('listing', [
            'listing' => $listing,
            'relatedListings' => $relatedListings
        ]);
    }

 public function getFeaturedProducts()
{
    $products = Listing::with(['images', 'category'])
        ->where('is_promoted', true)
        ->where('status', 'approved')
        ->inRandomOrder()
        ->paginate(10)
        ->through(function ($listing) {
            return [
                'id' => $listing->id,
                'title' => $listing->title,
                'price' => $listing->price,
                'description' => $listing->description,
                'location' => $listing->location,
                'city' => $listing->city,
                'category' => [
                    'id' => $listing->category->id,
                    'name' => $listing->category->name,
                ],
                'images' => $listing->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'image_url' => url('storage/' . $image->image_url)
                    ];
                })->toArray(),
                'created_at' => $listing->created_at,
            ];
        });

    return response()->json([
        'success' => true,
        'data' => $products->items(),
        'meta' => [
            'current_page' => $products->currentPage(),
            'total' => $products->total(),
            'per_page' => $products->perPage(),
            'last_page' => $products->lastPage(),
        ]
    ]);
}
 public function providerProducts()
    {
        $user = Auth::user();
        
        $products = Listing::where('user_id', $user->id)
            ->with('category:id,name')
            ->orderBy('created_at', 'desc')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $products,
            'message' => 'تم جلب المنتجات بنجاح'
        ]);
    }
    public function destroyApi($id)
{
    $listing = Listing::where('user_id', Auth::id())->findOrFail($id);
    $listing->delete();
    
    return response()->json([
        'success' => true,
        'message' => 'تم حذف المنتج بنجاح'
    ]);
}

public function storeAPI(Request $request)
{
    $request->validate([
        'title' => 'required|string|max:255',
        'description' => 'required|string',
        'price' => 'required|numeric|min:0',
        'category_id' => 'required|exists:categories,id',
        'city' => 'required|string|max:255',
        'location' => 'required|string|max:255',
        'images.*' => 'required|image|mimes:jpeg,png,jpg,webp|max:5120',
    ]);

    $listing = Listing::create([
        'title' => $request->title,
        'description' => $request->description,
        'price' => $request->price,
        'category_id' => $request->category_id,
        'country' => 'سوريا',
        'city' => $request->city,
        'location' => $request->location,
        'user_id' => Auth::id(),
        'is_promoted' => false,
        'status' => 'pending',
    ]);

    if ($request->hasFile('images')) {
        foreach ($request->file('images') as $image) {
            $path = $image->store('listings', 'public');
            
            ListingImage::create([
                'listing_id' => $listing->id,
                'image_url' => $path,
            ]);
        }
    }

    return response()->json([
        'success' => true,
        'message' => 'تم إضافة الإعلان بنجاح وهو قيد المراجعة',
        'data' => $listing->load(['category', 'images']),
    ]);
}
}