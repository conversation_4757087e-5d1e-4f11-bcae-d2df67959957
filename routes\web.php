<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ListingController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DigitalCardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\WishlistController;
use App\Models\User;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', [UserController::class, 'welcome'])->name('welcome');

// مسارات المصادقة (Breeze)
Route::middleware('guest')->group(function () {
    Route::get('register', [RegisteredUserController::class, 'create'])
                ->name('register');
    
    Route::post('register', [RegisteredUserController::class, 'store']);

    Route::get('login', [AuthenticatedSessionController::class, 'create'])
                ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store']);
});

Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
            ->name('logout');

// مسارات لوحة التحكم
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', function () {
        if (auth()->user()->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } elseif (auth()->user()->isProvider()) {
            return redirect()->route('provider.dashboard');
        }
        return redirect()->route('user.dashboard');
    })->name('dashboard');
    
    Route::prefix('wishlist')->group(function () {
        Route::get('/', [WishlistController::class, 'index'])->name('wishlist.index');
        Route::post('/{listing}', [WishlistController::class, 'store'])->name('wishlist.store');
        Route::delete('/{listing}', [WishlistController::class, 'destroy'])->name('wishlist.destroy');
    });

    // مسارات المستخدم العادي
    Route::middleware(['is-user'])->group(function () {
        Route::get('/user/dashboard', [UserController::class, 'dashboard'])->name('user.dashboard');
        Route::get('/ratings/given', [RatingController::class, 'given'])->name('ratings.given');
        
        // الطلبات
        Route::get('/orders', [OrderController::class, 'userIndex'])->name('orders.index');
        Route::get('/orders/{order}', [OrderController::class, 'userShow'])->name('orders.show');
        
        // المفضلة
        Route::get('/wishlist', [UserController::class, 'wishlist'])->name('wishlist.index');
        
        // التقييمات
        Route::get('/ratings', [UserController::class, 'ratings'])->name('ratings.index');
        
        // المعاملات المالية
        Route::get('/transactions', [UserController::class, 'transactions'])->name('transactions.index');
        
        // البطاقات الرقمية
        Route::get('/digital-cards', [DigitalCardController::class, 'index'])->name('digital-cards.index');
        Route::get('/digital-cards/{digitalCard}', [DigitalCardController::class, 'show'])->name('digital-cards.show');
        Route::get('/listingsUser', [ListingController::class, 'providerIndex'])->name('listings.index');
        
    });
Route::resource('listings', ListingController::class)->except(['show']);
    // مسارات البائع
    Route::middleware(['is-provider'])
        ->prefix('provider')
        ->as('provider.')
        ->group(function () {
            Route::get('/dashboard', [ProviderController::class, 'dashboard'])->name('dashboard');
            
            Route::get('/listings', [ListingController::class, 'providerIndex'])->name('listings.index');
            Route::resource('orders', OrderController::class)->only(['index', 'show', 'update']);
            Route::get('/ratings', [ProviderController::class, 'ratings'])->name('ratings.index');
            Route::resource('digital-cards', DigitalCardController::class);
        });

    // مسارات المدير
    Route::middleware(['is-admin'])->prefix('admin')->as('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // الإعلانات
        Route::get('/listings', [AdminController::class, 'listings'])->name('listings.index');
        Route::get('/listings/{listing}/review', [AdminController::class, 'reviewListing'])->name('listings.review');
        Route::post('/listings/{listing}/approve', [AdminController::class, 'approveListing'])->name('listings.approve');
        Route::post('/listings/{listing}/reject', [AdminController::class, 'rejectListing'])->name('listings.reject');
        Route::put('/listings/{listing}/status', [AdminController::class, 'updateStatus'])->name('listings.update-status');
        Route::post('/listings/{listing}/toggle-promoted', [AdminController::class, 'togglePromoted'])->name('listings.toggle-promoted');
        
        // الإبلاغات
        Route::get('/reports', [AdminController::class, 'reports'])->name('reports.index');
        Route::delete('/reports/{report}/dismiss', [AdminController::class, 'dismissReport'])->name('reports.dismiss');
        Route::delete('/listings/{listing}/delete', [AdminController::class, 'destroy'])->name('listings.delete');
        Route::get('/listings/{listing}/reviewR', [AdminController::class, 'reviewR'])->name('listings.reviewR');
        
        // المستخدمون
        Route::get('/users', [AdminController::class, 'users'])->name('users.index');
        Route::post('/users/{user}/verify', [AdminController::class, 'verifyUser'])->name('users.verify');

        // التصنيفات
        Route::get('/categories', [AdminController::class, 'categories'])->name('categories.index');
        Route::get('/categories/create', [AdminController::class, 'createCategory'])->name('categories.create');
        Route::post('/categories', [AdminController::class, 'storeCategory'])->name('categories.store');
        Route::get('/categories/{category}/edit', [AdminController::class, 'editCategory'])->name('categories.edit');
        Route::put('/categories/{category}', [AdminController::class, 'updateCategory'])->name('categories.update');
        Route::delete('/categories/{category}', [AdminController::class, 'destroyCategory'])->name('categories.destroy');

        //Network
        Route::get('/network', [AdminController::class, 'network'])->name('network.index');
         Route::get('/network/create', [AdminController::class, 'createnetwork'])->name('networks.create');
        Route::post('/network', [AdminController::class, 'storenetwork'])->name('networks.store');
        Route::get('/network/{network}/edit', [AdminController::class, 'editnetwork'])->name('networks.edit');
        Route::put('/network/{network}', [AdminController::class, 'updatenetwork'])->name('networks.update');
        Route::delete('/network/{network}', [AdminController::class, 'destroynetwork'])->name('networks.destroy');

        // الطلبات
        Route::get('/orders', [AdminController::class, 'orders'])->name('orders.index');

        // التقييمات
        Route::get('/ratings', [AdminController::class, 'ratings'])->name('ratings.index');
        Route::post('/ratings/{rating}/approve', [AdminController::class, 'approveRating'])->name('ratings.approve');
        Route::post('/ratings/{rating}/reject', [AdminController::class, 'rejectRating'])->name('ratings.reject');



        // الإعدادات
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings.index');
        Route::put('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');

        // البطاقات الرقمية
        Route::get('/digital-cards', [DigitalCardController::class, 'adminIndex'])->name('digital-cards.index');
        Route::get('/digital-cards/{digitalCard}', [DigitalCardController::class, 'adminShow'])->name('digital-cards.show');
        Route::post('/digital-cards', [DigitalCardController::class, 'store'])->name('digital-cards.store');
        Route::put('/digital-cards/{digitalCard}', [DigitalCardController::class, 'update'])->name('digital-cards.update');
        Route::delete('/digital-cards/{digitalCard}', [DigitalCardController::class, 'destroy'])->name('digital-cards.destroy');
    });

    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// تصنيفات
Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{category}', [CategoryController::class, 'show'])->name('categories.show');

// إعلانات - مسارات عامة
Route::get('/listings', [ListingController::class, 'index'])->name('listings.index');
Route::get('/listings/{listing}', [ListingController::class, 'show'])->name('listings.show');

// تقييمات
Route::post('/ratings', [RatingController::class, 'store'])->name('ratings.store')
    ->middleware('auth');

    Route::get('/vendors', function () {
    $vendors = User::where('type', 'provider')
                ->withCount('listings')
                ->withAvg('ratings', 'rating')
                ->withCount('ratings as reviews_count')
                ->paginate(12);
           
    return view('vendors', compact('vendors'));
})->name('vendors');

    Route::get('about', function () {

           
    return view('about');
})->name('about');

Route::get('/categories/{category}/listings', [CategoryController::class, 'listings'])->name('category.listings');


    Route::get('/categories/{category}', [CategoryController::class, 'show'])
    ->name('categories.show');


    Route::get('/networkCat', [CategoryController::class, 'networkCat'])->name('networkCat.index');