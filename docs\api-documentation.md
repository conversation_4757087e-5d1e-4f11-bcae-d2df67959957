# 🔗 API Documentation - Mega Platform

## 📋 فهرس المحتويات
- [نظرة عامة](#نظرة-عامة)
- [المصادقة](#المصادقة)
- [تنسيق الاستجابات](#تنسيق-الاستجابات)
- [رموز الأخطاء](#رموز-الأخطاء)
- [Authentication APIs](#authentication-apis)
- [Categories APIs](#categories-apis)
- [Products/Listings APIs](#productslistings-apis)
- [أمثلة عملية](#أمثلة-عملية)

---

## 🌟 نظرة عامة

**Base URL**: `https://yourdomain.com/api/`

جميع APIs تستخدم:
- **Content-Type**: `application/json`
- **Accept**: `application/json`
- **Rate Limiting**: 60 طلب/دقيقة لكل IP

---

## 🔐 المصادقة

### Laravel Sanctum Token Authentication

#### الحصول على Token
```http
POST /api/login
```

#### استخدام Token
```http
Authorization: Bearer {your-token-here}
```

#### مثال Header كامل
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer 1|abc123def456ghi789...
```

---

## 📊 تنسيق الاستجابات

### استجابة ناجحة
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // البيانات المطلوبة
    },
    "meta": {
        "current_page": 1,
        "total": 100,
        "per_page": 15
    }
}
```

### استجابة خطأ
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Error message"]
    },
    "error_code": "VALIDATION_ERROR"
}
```

---

## ⚠️ رموز الأخطاء

| Code | Status | Description |
|------|--------|-------------|
| `200` | OK | طلب ناجح |
| `201` | Created | تم إنشاء المورد بنجاح |
| `400` | Bad Request | خطأ في البيانات المرسلة |
| `401` | Unauthorized | غير مصرح بالوصول |
| `403` | Forbidden | ممنوع الوصول |
| `404` | Not Found | المورد غير موجود |
| `422` | Validation Error | خطأ في التحقق من البيانات |
| `429` | Too Many Requests | تجاوز حد الطلبات |
| `500` | Server Error | خطأ في الخادم |

---

## 🔑 Authentication APIs

### 1. تسجيل مستخدم جديد

```http
POST /api/register
```

**المعاملات المطلوبة:**
```json
{
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+963987654321",
    "password": "password123",
    "password_confirmation": "password123",
    "type": "user"
}
```

**المعاملات الاختيارية:**
```json
{
    "interests": [1, 2, 3],
    "license_number": "123456789",
    "vehicle_type": "car",
    "vehicle_model": "Toyota Camry",
    "vehicle_plate_number": "ABC123"
}
```

**استجابة ناجحة (201):**
```json
{
    "success": true,
    "token": "1|abc123def456...",
    "user": {
        "id": 1,
        "name": "أحمد محمد",
        "email": "<EMAIL>",
        "phone": "+963987654321",
        "type": "user",
        "is_verified": true,
        "created_at": "2025-01-12T10:30:00.000000Z"
    },
    "message": "تم التسجيل بنجاح!"
}
```

### 2. تسجيل الدخول

```http
POST /api/login
```

**المعاملات المطلوبة:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "token": "2|def456ghi789...",
        "user": {
            "id": 1,
            "name": "أحمد محمد",
            "email": "<EMAIL>",
            "type": "user"
        }
    }
}
```

### 3. تسجيل الخروج

```http
POST /api/logout
```

**Headers مطلوبة:**
```http
Authorization: Bearer {token}
```

**استجابة ناجحة (200):**
```json
{
    "message": "Logged out successfully!"
}
```

### 4. بيانات المستخدم الحالي

```http
GET /api/user
```

**Headers مطلوبة:**
```http
Authorization: Bearer {token}
```

**استجابة ناجحة (200):**
```json
{
    "id": 1,
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+963987654321",
    "type": "user",
    "is_verified": true,
    "email_verified_at": null,
    "created_at": "2025-01-12T10:30:00.000000Z",
    "updated_at": "2025-01-12T10:30:00.000000Z"
}
```

---

## 🏷️ Categories APIs

### 1. جلب جميع الفئات الرئيسية

```http
GET /api/categories
```

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "إلكترونيات",
            "icon": "electronics-icon.png"
        },
        {
            "id": 2,
            "name": "ملابس",
            "icon": "clothes-icon.png"
        }
    ]
}
```

### 2. جلب منتجات فئة معينة

```http
GET /api/categories/{category_id}/listings
```

**معاملات URL:**
- `category_id`: رقم الفئة

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "data": {
        "products": [
            {
                "id": 1,
                "title": "iPhone 15 Pro",
                "description": "أحدث هاتف من آبل",
                "price": "1200.00",
                "category": {
                    "id": 1,
                    "name": "إلكترونيات"
                },
                "images": [
                    {
                        "id": 1,
                        "image_url": "storage/listings/iphone15.jpg"
                    }
                ],
                "created_at": "2025-01-12T10:30:00.000000Z"
            }
        ],
        "child_categories": [
            {
                "id": 3,
                "name": "هواتف ذكية",
                "icon": "phones-icon.png"
            }
        ]
    }
}
```

### 3. جلب فئات الشبكات

```http
GET /api/networkCatAPI
```

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "شبكة سيريتل",
            "ip_range": "***********/24"
        },
        {
            "id": 2,
            "name": "شبكة MTN",
            "ip_range": "10.0.0.0/8"
        }
    ]
}
```

---

## 📦 Products/Listings APIs

### 1. جلب المنتجات المميزة

```http
GET /api/products/featured
```

**⚠️ ملاحظة**: هذا API يحتاج إصلاح - حالياً مقيد بـ ID=4 فقط

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 4,
            "title": "منتج مميز",
            "description": "وصف المنتج",
            "price": "299.99",
            "is_promoted": true,
            "images": [
                {
                    "id": 1,
                    "image_url": "storage/listings/product.jpg"
                }
            ]
        }
    ]
}
```

### 2. جلب منتجات البائع (محمي)

```http
GET /api/provider/products
```

**Headers مطلوبة:**
```http
Authorization: Bearer {token}
```

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "منتج البائع",
            "description": "وصف المنتج",
            "price": "150.00",
            "status": "approved",
            "category": {
                "id": 1,
                "name": "إلكترونيات"
            },
            "created_at": "2025-01-12T10:30:00.000000Z"
        }
    ],
    "message": "تم جلب المنتجات بنجاح"
}
```

### 3. إضافة منتج جديد (محمي)

```http
POST /api/provider/products
```

**Headers مطلوبة:**
```http
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**المعاملات المطلوبة:**
```json
{
    "title": "عنوان المنتج",
    "description": "وصف تفصيلي للمنتج",
    "price": "299.99",
    "category_id": 1,
    "city": "دمشق",
    "location": "المزة",
    "images[]": "ملفات الصور (حد أقصى 5MB لكل صورة)"
}
```

**استجابة ناجحة (201):**
```json
{
    "success": true,
    "data": {
        "id": 15,
        "title": "عنوان المنتج",
        "description": "وصف تفصيلي للمنتج",
        "price": "299.99",
        "category_id": 1,
        "city": "دمشق",
        "location": "المزة",
        "user_id": 5,
        "status": "pending",
        "is_promoted": false,
        "created_at": "2025-01-12T11:00:00.000000Z"
    },
    "message": "تم إضافة المنتج بنجاح وهو قيد المراجعة"
}
```

### 4. حذف منتج (محمي)

```http
DELETE /api/provider/products/{product_id}
```

**Headers مطلوبة:**
```http
Authorization: Bearer {token}
```

**معاملات URL:**
- `product_id`: رقم المنتج

**استجابة ناجحة (200):**
```json
{
    "success": true,
    "message": "تم حذف المنتج بنجاح"
}
```

---

## 🔧 أمثلة عملية

### مثال كامل - تسجيل دخول وجلب المنتجات

#### 1. تسجيل الدخول
```bash
curl -X POST https://yourdomain.com/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### 2. استخدام Token لجلب المنتجات
```bash
curl -X GET https://yourdomain.com/api/provider/products \
  -H "Authorization: Bearer 2|def456ghi789..." \
  -H "Accept: application/json"
```

#### 3. إضافة منتج جديد
```bash
curl -X POST https://yourdomain.com/api/provider/products \
  -H "Authorization: Bearer 2|def456ghi789..." \
  -F "title=iPhone 15 Pro Max" \
  -F "description=أحدث هاتف من آبل مع كاميرا متطورة" \
  -F "price=1500" \
  -F "category_id=1" \
  -F "city=دمشق" \
  -F "location=المزة" \
  -F "images[]=@/path/to/image1.jpg" \
  -F "images[]=@/path/to/image2.jpg"
```

---

## 🚨 APIs المفقودة (قيد التطوير)

### Orders APIs
```http
GET    /api/orders              # قائمة الطلبات
POST   /api/orders              # إنشاء طلب جديد
GET    /api/orders/{id}         # تفاصيل طلب
PUT    /api/orders/{id}/status  # تحديث حالة الطلب
```

### Ratings APIs
```http
GET    /api/products/{id}/ratings    # تقييمات منتج
POST   /api/products/{id}/ratings    # إضافة تقييم
GET    /api/user/ratings             # تقييمات المستخدم
```

### Wishlist APIs
```http
GET    /api/wishlist                 # قائمة الأمنيات
POST   /api/wishlist/{product_id}    # إضافة للأمنيات
DELETE /api/wishlist/{product_id}    # حذف من الأمنيات
```

### Search APIs
```http
GET /api/search?q={query}            # البحث في المنتجات
GET /api/products?filter[]=...       # فلترة المنتجات
```

---

## 📝 ملاحظات مهمة

### أمان
- ⚠️ **احذف فوراً**: `/api/destroy-project` endpoint خطير جداً
- 🔒 استخدم HTTPS في الإنتاج
- 🔑 احفظ tokens بشكل آمن في التطبيق
- ⏰ tokens لا تنتهي صلاحيتها (يمكن تعديل هذا)

### أداء
- 📊 Rate limiting: 60 طلب/دقيقة
- 📄 لا يوجد pagination في معظم APIs (يحتاج إضافة)
- 🖼️ الصور غير محسنة للموبايل (يحتاج ضغط)

### تطوير
- 🧪 لا توجد اختبارات للـ APIs
- 📖 يحتاج Swagger/OpenAPI documentation
- 🔄 يحتاج API versioning

---

*آخر تحديث: يناير 2025*
